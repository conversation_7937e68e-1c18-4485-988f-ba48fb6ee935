APP_NAME="Stride Middleware API"
APP_ENV=local
APP_KEY=base64:45K0ufb31SCYtNXD5hyLSIhvhVDXPCtGwNaairujRoE=
APP_DEBUG=true
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=file
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Okta SSO Configuration
# Based on your Expo configuration - using 'default' authorization server
OKTA_DOMAIN=integrator-5743111.okta.com
OKTA_AUTH_SERVER_ID=default
OKTA_CLIENT_ID=0oauipzrsuO08tffD697
OKTA_CLIENT_SECRET=your_client_secret_from_okta_app
OKTA_REDIRECT_URI=http://localhost:8000/auth/callback

# Mobile App Configuration (matches your Expo app scheme)
APP_SCHEME=learningcoachcommunity

# Frontend Configuration (for web platform redirects)
FRONTEND_URL=http://localhost:3000

# Integration Testing (set to true to enable real Okta API tests)
OKTA_INTEGRATION_TEST=false
