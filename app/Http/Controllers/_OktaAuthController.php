<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSession;
use App\Services\OktaService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

/// Deprecated; Reference
class AuthController extends Controller
{
    private OktaService $oktaService;

    public function __construct(OktaService $oktaService)
    {
        $this->oktaService = $oktaService;
    }

    /**
     * Initiate Okta login flow.
     */
    public function login(Request $request): \Illuminate\Http\RedirectResponse
    {
        $platform = $request->get('platform', 'mobile');

        // Store platform in session for callback handling
        session(['auth_platform' => $platform]);

        // Generate state and PKCE challenge
        $state = Str::random(40);
        $pkce = $this->oktaService->generatePkceChallenge();

        // Store state and PKCE in session for validation
        session([
            'oauth_state' => $state,
            'code_verifier' => $pkce['code_verifier'],
        ]);

        // Build authorization URL
        $authUrl = $this->oktaService->buildAuthorizationUrl($state, $pkce);

        Log::info('Okta login initiated', [
            'platform' => $platform,
            'state' => $state,
        ]);

        return redirect($authUrl);
    }

    /**
     * Handle Okta callback.
     */
    public function callback(Request $request): \Illuminate\Http\RedirectResponse
    {
        try {
            // Validate state parameter
            $state = $request->get('state');
            $sessionState = session('oauth_state');

            if (!$state || $state !== $sessionState) {
                throw new \Exception('Invalid state parameter');
            }

            // Get authorization code
            $code = $request->get('code');
            if (!$code) {
                throw new \Exception('No authorization code received');
            }

            // Get code verifier from session
            $codeVerifier = session('code_verifier');
            if (!$codeVerifier) {
                throw new \Exception('No code verifier found in session');
            }

            // Exchange code for tokens
            $tokens = $this->oktaService->exchangeCodeForTokens($code, $codeVerifier);

            // Get user profile from Okta
            $oktaProfile = $this->oktaService->getUserProfile($tokens['access_token']);

            // Create or update user
            $user = User::createOrUpdateFromOkta($oktaProfile);

            // Create user session with encrypted Okta tokens
            $userSession = $this->createUserSession($user, $tokens, $oktaProfile, $request);

            // Generate app token for React Native
            $appToken = $user->createToken('mobile-app', ['*'], now()->addDays(30))->plainTextToken;

            // Store app token hash for validation
            $userSession->update(['app_token_hash' => hash('sha256', $appToken)]);

            // Get platform and redirect accordingly
            $platform = session('auth_platform', 'mobile');

            // Clear session data
            session()->forget(['oauth_state', 'code_verifier', 'auth_platform']);

            $userInfo = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'okta_user_id' => $user->okta_user_id,
            ];

            Log::info('Okta authentication successful', [
                'user_id' => $user->id,
                'platform' => $platform,
            ]);

            if ($platform === 'web') {
                // Web: Redirect to success page with token in URL
                return redirect()->to("/auth/success?token={$appToken}&user=" . urlencode(json_encode($userInfo)));
            } else {
                // Mobile: Use custom scheme
                $appScheme = config('app.scheme', 'myapp');
                return redirect("{$appScheme}://auth/success?token={$appToken}&user=" . urlencode(json_encode($userInfo)));
            }

        } catch (\Exception $e) {
            Log::error('Okta callback failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            $platform = session('auth_platform', 'mobile');
            $errorMessage = urlencode($e->getMessage());

            // Clear session data
            session()->forget(['oauth_state', 'code_verifier', 'auth_platform']);

            if ($platform === 'web') {
                return redirect()->to("/auth/error?error={$errorMessage}");
            } else {
                $appScheme = config('app.scheme', 'myapp');
                return redirect("{$appScheme}://auth/error?error={$errorMessage}");
            }
        }
    }

    /**
     * Get authenticated user information.
     */
    public function user(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'okta_user_id' => $user->okta_user_id,
            'okta_profile_data' => $user->okta_profile_data,
            'email_verified_at' => $user->email_verified_at,
        ]);
    }

    /**
     * Refresh app token.
     */
    public function refresh(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $currentToken = $request->bearerToken();

            if (!$currentToken) {
                return response()->json(['error' => 'No token provided'], 401);
            }

            // Find the user session for this token
            $tokenHash = hash('sha256', $currentToken);
            $userSession = $user->activeUserSessions()
                ->where('app_token_hash', $tokenHash)
                ->first();

            if (!$userSession) {
                return response()->json(['error' => 'Invalid session'], 401);
            }

            // Check if Okta session is still valid
            if (!$userSession->isOktaSessionValid()) {
                // Try to refresh Okta token
                if ($userSession->okta_refresh_token) {
                    try {
                        $refreshedTokens = $this->oktaService->refreshAccessToken($userSession->okta_refresh_token);

                        // Update session with new tokens
                        $userSession->update([
                            'okta_access_token' => $refreshedTokens['access_token'],
                            'okta_expires_at' => now()->addSeconds($refreshedTokens['expires_in'] ?? 3600),
                            'last_activity_at' => now(),
                        ]);

                        // Update refresh token if provided
                        if (isset($refreshedTokens['refresh_token'])) {
                            $userSession->update(['okta_refresh_token' => $refreshedTokens['refresh_token']]);
                        }

                    } catch (\Exception $e) {
                        Log::error('Okta token refresh failed during app token refresh', [
                            'user_id' => $user->id,
                            'error' => $e->getMessage(),
                        ]);

                        $userSession->deactivate();
                        return response()->json(['error' => 'Okta session expired'], 401);
                    }
                } else {
                    $userSession->deactivate();
                    return response()->json(['error' => 'Okta session expired'], 401);
                }
            }

            // Revoke current token
            $request->user()->currentAccessToken()->delete();

            // Generate new app token
            $newToken = $user->createToken('mobile-app', ['*'], now()->addDays(30))->plainTextToken;

            // Update session with new token hash
            $userSession->update([
                'app_token_hash' => hash('sha256', $newToken),
                'last_activity_at' => now(),
            ]);

            Log::info('App token refreshed', [
                'user_id' => $user->id,
                'session_id' => $userSession->id,
            ]);

            return response()->json(['token' => $newToken]);

        } catch (\Exception $e) {
            Log::error('Token refresh failed', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
            ]);

            return response()->json(['error' => 'Token refresh failed'], 500);
        }
    }

    /**
     * Logout user and revoke tokens.
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $currentToken = $request->bearerToken();

            if ($currentToken) {
                // Find and deactivate the user session
                $tokenHash = hash('sha256', $currentToken);
                $userSession = $user->activeUserSessions()
                    ->where('app_token_hash', $tokenHash)
                    ->first();

                if ($userSession) {
                    // Revoke Okta tokens
                    if ($userSession->okta_access_token) {
                        $this->oktaService->revokeToken($userSession->okta_access_token, 'access_token');
                    }

                    if ($userSession->okta_refresh_token) {
                        $this->oktaService->revokeToken($userSession->okta_refresh_token, 'refresh_token');
                    }

                    // Deactivate session
                    $userSession->deactivate();
                }
            }

            // Revoke current app token
            $request->user()->currentAccessToken()->delete();

            Log::info('User logged out', [
                'user_id' => $user->id,
            ]);

            return response()->json(['message' => 'Successfully logged out']);

        } catch (\Exception $e) {
            Log::error('Logout failed', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
            ]);

            return response()->json(['error' => 'Logout failed'], 500);
        }
    }

    /**
     * Create user session with Okta tokens.
     */
    private function createUserSession(User $user, array $tokens, array $oktaProfile, Request $request): UserSession
    {
        $platform = session('auth_platform', 'mobile');

        // Deactivate any existing active sessions for this platform
        $user->activeUserSessions()
            ->where('platform', $platform)
            ->update(['is_active' => false]);

        return UserSession::create([
            'user_id' => $user->id,
            'okta_access_token' => $tokens['access_token'],
            'okta_refresh_token' => $tokens['refresh_token'] ?? null,
            'okta_id_token' => $tokens['id_token'] ?? null,
            'okta_expires_at' => now()->addSeconds($tokens['expires_in'] ?? 3600),
            'platform' => $platform,
            'okta_session_id' => $oktaProfile['sub'] ?? null,
            'okta_user_data' => $oktaProfile,
            'state' => session('oauth_state'),
            'code_verifier' => session('code_verifier'),
            'is_active' => true,
            'last_activity_at' => now(),
        ]);
    }
}
